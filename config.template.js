/**
 * Configuration Template for LinkedIn Recruiter <PERSON>
 * 
 * INSTRUCTIONS:
 * 1. Copy this file to 'config.js'
 * 2. Replace the placeholder values with your personal information
 * 3. Never commit config.js to version control (it's in .gitignore)
 * 
 * <AUTHOR> - https://github.com/fabiomvalente
 */

// Personal Information Configuration
const CONFIG = {
    // Your full name as it should appear in messages
    MY_NAME: "Your Full Name Here",
    
    // Your current position/job title
    MY_POSITION: "Your Current Position or Job Title",
    
    // Your area of expertise or specialization
    POS_SEARCH: "Your Area of Expertise or Specialization",
    
    // Default connection limits
    DEFAULT_LIMIT: 100,        // Default limit for non-premium users
    PREMIUM_LIMIT: 200,        // Limit for premium users
    
    // Message template customization
    MESSAGE_SETTINGS: {
        // Set to true to include a personalized note with connection requests
        INCLUDE_NOTE: true,
        
        // Custom message template (you can modify this)
        // Available variables: {firstName}, {<PERSON>Y_NAME}, {MY_POSITION}, {P<PERSON>_SEARCH}
        TEMPLATE: `Hi {firstName}, I hope you're doing well!  

I'm a {MY_POSITION} from Brazil with experience in {POS_SEARCH}, seeking international opportunities.  
I'd love to connect and expand my network.  

Best regards,  
    {MY_NAME}`
    }
};

// Export configuration for use in main script
if (typeof window !== 'undefined') {
    window.LINKEDIN_CONFIG = CONFIG;
}
