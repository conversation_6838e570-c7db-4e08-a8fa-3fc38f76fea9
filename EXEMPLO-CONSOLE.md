# 🖥️ Como Usar no Console do Browser

## 📋 **Passo a Passo Detalhado**

### 1. **Prepare o Arquivo de Configuração**
```bash
# Copie o template
copy linkedin-addon\config.template.js config.js

# Edite config.js com suas informações:
```

**Exemplo do config.js editado:**
```javascript
const MY_NAME = "João Silva";
const MY_POSITION = "Desenvolvedor Full-Stack";
const POS_SEARCH = "React, Node.js e Cloud";

const MESSAGE_TEMPLATE_TEXT = `Ol<PERSON> {firstName}, espero que esteja bem!

Sou {MY_POSITION} do Brasil com experiência em {POS_SEARCH}, buscando oportunidades internacionais.
Gostaria de me conectar e expandir minha rede.

Atenciosamente,
    {MY_NAME}`;

const LINKEDIN_CONFIG = {
    MY_NAME: MY_NAME,
    MY_POSITION: MY_POSITION,
    POS_SEARCH: POS_SEARCH,
    MESSAGE_TEMPLATE: {
        TEXT: MESSAGE_TEMPLATE_TEXT,
        INCLUDE_NOTE: true
    },
    DEFAULT_LIMIT: 100,
    PREMIUM_LIMIT: 200,
    MIN_DELAY: 1000,
    MAX_DELAY: 3000
};

if (typeof window !== 'undefined') {
    window.LINKEDIN_CONFIG = LINKEDIN_CONFIG;
    window.MY_NAME = MY_NAME;
    window.MY_POSITION = MY_POSITION;
    window.POS_SEARCH = POS_SEARCH;
}
```

### 2. **Abrir LinkedIn e Console**
1. Vá para LinkedIn.com
2. Navegue para uma página de busca de pessoas/recrutadores
3. Pressione **F12** para abrir Developer Tools
4. Clique na aba **Console**

### 3. **Carregar Configuração**
1. **Abra o arquivo `config.js`** no seu editor de texto
2. **Selecione todo o conteúdo** (Ctrl+A)
3. **Copie** (Ctrl+C)
4. **Cole no console** e pressione **Enter**

✅ **Resultado esperado**: Você verá algo como:
```
> const MY_NAME = "João Silva";
undefined
```

### 4. **Carregar Script Principal**
1. **Abra o arquivo `Adiciona Recrutadores Avançado.js`**
2. **Selecione todo o conteúdo** (Ctrl+A)
3. **Copie** (Ctrl+C)
4. **Cole no console** e pressione **Enter**

✅ **Resultado esperado**: O painel de controle aparece no canto da tela!

### 5. **Usar o Script**
1. Configure as opções no painel:
   - ✅ Marque "Modo Teste" para validar mensagens
   - ✅ Defina limite de conexões
   - ✅ Marque "Premium User" se aplicável

2. Clique **"Start"** para iniciar

## 🧪 **Testando o Sistema**

### **Modo Teste com Limite = 1**
- Script para após cada tentativa
- Mostra preview da mensagem no console
- Você confirma manualmente cada envio
- Perfeito para validar o template

### **Modo Teste com Limite > 1**
- Script mostra preview no console
- Cancela automaticamente o convite
- Continua para próximo perfil
- Perfeito para ver vários exemplos sem enviar

## 🔧 **Solução de Problemas**

### **Erro: "Configurações não encontradas"**
- Certifique-se de executar o config.js ANTES do script principal
- Verifique se não há erros de sintaxe no config.js

### **Erro: "MY_NAME is not defined"**
- O config.js não foi carregado corretamente
- Recarregue a página e tente novamente

### **Painel não aparece**
- Verifique se está em uma página do LinkedIn
- Recarregue a página e execute os scripts novamente

### **Script não encontra botões**
- Certifique-se de estar em uma página de busca de pessoas
- Aguarde a página carregar completamente

## 💡 **Dicas Importantes**

1. **Sempre teste primeiro** com Modo Teste ativado
2. **Use limites baixos** para começar (ex: 5-10 conexões)
3. **Monitore o console** para ver logs detalhados
4. **Recarregue a página** se algo der errado
5. **Respeite os limites** do LinkedIn para evitar restrições

## 🔄 **Para Atualizações**

Quando o script principal for atualizado:
1. Execute `python build-local.py` para gerar nova versão
2. Recarregue a página do LinkedIn
3. Execute novamente: config.js → script principal
