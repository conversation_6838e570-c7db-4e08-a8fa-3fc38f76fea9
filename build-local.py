import zipfile
from pathlib import Path
import re

# Caminhos
orig_script = Path("Adiciona Recrutadores Avançado.js")
dest_dir = Path("linkedin-addon")
dest_script = dest_dir / "script.js"
zip_path = dest_dir / "linkedin-addon-local.zip"

# Carrega o conteúdo original
with open(orig_script, "r", encoding="utf-8") as f:
    content = f.read().strip()

# Remove javascript:(function () { e })();
if content.startswith("javascript:"):
    content = content[len("javascript:"):].strip()
if content.startswith("(function () {"):
    content = content[len("(function () {"):].strip()
if content.endswith("})();"):
    content = content[:-len("})();")].strip()

# Detecta a linha const SCRIPT_VERSION = "x.y";
version_match = re.search(r'const\s+SCRIPT_VERSION\s*=\s*"([0-9.]+)"', content)
if not version_match:
    raise ValueError("❌ Versão do script não encontrada. Adicione 'const SCRIPT_VERSION = \"x.y\";' no topo do JS.")

script_version = version_match.group(1)

# Cria diretório destino se não existir
dest_dir.mkdir(exist_ok=True)

# Salva script limpo com dados pessoais mantidos
with open(dest_script, "w", encoding="utf-8") as f:
    f.write(content)

print(f"✅ [PESSOAL] script.js gerado com versão {script_version}")

# Gera o zip dentro da pasta linkedin-addon
with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
    for file in dest_dir.rglob("*"):
        if file.name != zip_path.name:
            zipf.write(file, arcname=file.relative_to(dest_dir))

print(f"✅ [PESSOAL] ZIP criado em: {zip_path}")