import zipfile
from pathlib import Path
import re
import shutil
import json

# <PERSON>in<PERSON>
orig_script = Path("Adiciona Recrutadores Avançado.js")
dest_dir = Path("linkedin-addon")
dest_script = dest_dir / "script.js"
config_template = Path("config.template.js")
addon_config_template = dest_dir / "config.template.js"
addon_config = dest_dir / "config.js"
manifest_path = dest_dir / "manifest.json"
zip_path = dest_dir / "linkedin-addon-local.zip"

def check_config_exists():
    """Verifica se o arquivo config.js existe na pasta da extensão"""
    return addon_config.exists()

def create_config_from_template():
    """Cria config.js a partir do template se não existir"""
    if not addon_config.exists() and addon_config_template.exists():
        print("📝 Criando config.js a partir do template...")
        with open(addon_config_template, "r", encoding="utf-8") as f:
            content = f.read()
        with open(addon_config, "w", encoding="utf-8") as f:
            f.write(content)
        print("⚠️  IMPORTANTE: Edite o arquivo config.js com suas informações pessoais!")
        return True
    return False

def update_manifest_version(version):
    """Atualiza a versão no manifest.json"""
    if manifest_path.exists():
        with open(manifest_path, "r", encoding="utf-8") as f:
            manifest = json.loads(f.read())
        manifest["version"] = version
        with open(manifest_path, "w", encoding="utf-8") as f:
            json.dump(manifest, f, indent=2)
        print(f"📦 Manifest atualizado para versão {version}")

# Carrega o conteúdo original
with open(orig_script, "r", encoding="utf-8") as f:
    content = f.read().strip()

# Remove javascript:(function () { e })();
if content.startswith("javascript:"):
    content = content[len("javascript:"):].strip()
if content.startswith("(function () {"):
    content = content[len("(function () {"):].strip()
if content.endswith("})();"):
    content = content[:-len("})();")].strip()

# Detecta a linha const SCRIPT_VERSION = "x.y";
version_match = re.search(r'const\s+SCRIPT_VERSION\s*=\s*"([0-9.]+)"', content)
if not version_match:
    raise ValueError("❌ Versão do script não encontrada. Adicione 'const SCRIPT_VERSION = \"x.y\";' no topo do JS.")

script_version = version_match.group(1)

# Cria diretório destino se não existir
dest_dir.mkdir(exist_ok=True)

# Verifica e cria configuração se necessário
config_created = create_config_from_template()
config_exists = check_config_exists()

if not config_exists:
    print("❌ ERRO: Arquivo config.js não encontrado!")
    print("   1. Copie config.template.js para config.js na pasta linkedin-addon")
    print("   2. Preencha suas informações pessoais")
    print("   3. Execute este script novamente")
    exit(1)

# Salva script limpo
with open(dest_script, "w", encoding="utf-8") as f:
    f.write(content)

# Atualiza versão no manifest
update_manifest_version(script_version)

print(f"✅ script.js gerado com versão {script_version}")
if config_created:
    print("⚠️  LEMBRE-SE: Edite o config.js com suas informações antes de usar!")

# Gera o zip dentro da pasta linkedin-addon (excluindo arquivos pessoais)
excluded_files = {zip_path.name, "config.js", ".DS_Store", "Thumbs.db"}

with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
    for file in dest_dir.rglob("*"):
        if file.is_file() and file.name not in excluded_files:
            zipf.write(file, arcname=file.relative_to(dest_dir))

print(f"✅ ZIP criado em: {zip_path}")
print("📦 Arquivos incluídos no ZIP:")
with zipfile.ZipFile(zip_path, 'r') as zipf:
    for name in sorted(zipf.namelist()):
        print(f"   - {name}")

print("\n🔒 Arquivos excluídos (dados pessoais):")
for excluded in excluded_files:
    if (dest_dir / excluded).exists():
        print(f"   - {excluded}")

print(f"\n🎯 Para usar a extensão:")
print(f"   1. Extraia o ZIP ou use a pasta linkedin-addon/")
print(f"   2. Copie config.template.js para config.js")
print(f"   3. Preencha suas informações pessoais no config.js")
print(f"   4. Carregue a extensão no navegador")