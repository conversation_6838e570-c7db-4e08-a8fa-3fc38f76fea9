/**
 * EXEMPLO de Configuration para LinkedIn Recruiter Browser Extension
 * 
 * Este é um EXEMPLO mostrando como preencher o config.js
 * NÃO use estes dados - são apenas exemplos!
 * 
 * Para usar:
 * 1. Copie config.template.js para config.js
 * 2. Edite config.js com SUAS informações reais
 * 
 * <AUTHOR> - https://github.com/fabiomvalente
 * @version 1.4
 */

// EXEMPLO - NÃO USE ESTES DADOS!

const LINKEDIN_CONFIG = {
    // ==========================================
    // INFORMAÇÕES PESSOAIS (EXEMPLO)
    // ==========================================
    
    MY_NAME: "<PERSON>",
    MY_POSITION: "Desenvolvedor Full-Stack",
    POS_SEARCH: "React, Node.js e Tecnologias Cloud",
    
    // ==========================================
    // TEMPLATE DE MENSAGEM (EXEMPLO)
    // ==========================================
    
    MESSAGE_TEMPLATE: {
        TEXT: `Olá {firstName}, espero que esteja bem!

Sou {MY_POSITION} do Brasil com experiência em {POS_SEARCH}, buscando oportunidades internacionais.
Gostaria de me conectar e expandir minha rede profissional.

Atenciosamente,
    {MY_NAME}`,
        
        INCLUDE_NOTE: true
    },
    
    // ==========================================
    // CONFIGURAÇÕES DE AUTOMAÇÃO (EXEMPLO)
    // ==========================================
    
    DEFAULT_LIMIT: 50,         // Limite conservador
    PREMIUM_LIMIT: 150,        // Limite premium conservador
    
    MIN_DELAY: 2000,          // 2 segundos mínimo
    MAX_DELAY: 4000,          // 4 segundos máximo
    SCROLL_DELAY: 6000,       // 6 segundos após scroll
    
    TEST_MODE: {
        ENABLED: true,         // Ativar modo teste por padrão
        PAUSE_BEFORE_SEND: true,
        SHOW_MESSAGE_PREVIEW: true,
        MAX_TEST_CONNECTIONS: 5
    },
    
    // ==========================================
    // CONFIGURAÇÕES DA INTERFACE (EXEMPLO)
    // ==========================================
    
    UI: {
        PANEL_POSITION: 'top-right',
        DETAILED_LOGS: true,
        AUTO_SCROLL: true
    }
};

// ==========================================
// EXPORTAÇÃO E COMPATIBILIDADE
// ==========================================

if (typeof window !== 'undefined') {
    window.LINKEDIN_CONFIG = LINKEDIN_CONFIG;
    
    // Backward compatibility
    window.MY_NAME = LINKEDIN_CONFIG.MY_NAME;
    window.MY_POSITION = LINKEDIN_CONFIG.MY_POSITION;
    window.POS_SEARCH = LINKEDIN_CONFIG.POS_SEARCH;
    window.MESSAGE_TEMPLATE_TEXT = LINKEDIN_CONFIG.MESSAGE_TEMPLATE.TEXT;
}
