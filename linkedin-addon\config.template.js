/**
 * Configuration Template for LinkedIn Recruiter Browser Extension
 *
 * SETUP INSTRUCTIONS:
 * 1. Copy this file to 'config.js' in the same folder
 * 2. Replace the placeholder values with your personal information
 * 3. Never commit config.js to version control (it's in .gitignore)
 *
 * <AUTHOR> - https://github.com/fabiomvalente
 * @version 1.4
 */

// ⚠️ REPLACE THESE VALUES WITH YOUR PERSONAL INFORMATION ⚠️

// Your full name as it should appear in messages
const MY_NAME = "Your Full Name Here";

// Your current position/job title
const MY_POSITION = "Your Current Position or Job Title";

// Your area of expertise or specialization
const POS_SEARCH = "Your Area of Expertise or Specialization";

// Message template (customize as needed)
const MESSAGE_TEMPLATE_TEXT = `Hi {firstName}, I hope you're doing well!

I'm a {MY_POSITION} from Brazil with experience in {POS_SEARCH}, seeking international opportunities.
I'd love to connect and expand my network.

Best regards,
    {MY_NAME}`;

// Configuration object for the script
const LINKEDIN_CONFIG = {
    MY_NAME: MY_NAME,
    MY_POSITION: MY_POSITION,
    POS_SEARCH: POS_SEARCH,

    MESSAGE_TEMPLATE: {
        TEXT: MESSAGE_TEMPLATE_TEXT,
        INCLUDE_NOTE: true
    },

    // Default limits
    DEFAULT_LIMIT: 100,
    PREMIUM_LIMIT: 200,

    // Timing (milliseconds)
    MIN_DELAY: 1000,
    MAX_DELAY: 3000
};

// Make configuration available globally
if (typeof window !== 'undefined') {
    window.LINKEDIN_CONFIG = LINKEDIN_CONFIG;

    // Backward compatibility
    window.MY_NAME = MY_NAME;
    window.MY_POSITION = MY_POSITION;
    window.POS_SEARCH = POS_SEARCH;
}
