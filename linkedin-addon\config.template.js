/**
 * Configuration Template for LinkedIn Recruiter Browser Extension
 * 
 * SETUP INSTRUCTIONS:
 * 1. Copy this file to 'config.js' in the same folder
 * 2. Replace the placeholder values with your personal information
 * 3. Never commit config.js to version control (it's in .gitignore)
 * 
 * <AUTHOR> - https://github.com/fabiomvalente
 * @version 1.4
 */

// ⚠️ REPLACE THESE VALUES WITH YOUR PERSONAL INFORMATION ⚠️

// Your full name as it should appear in messages
const MY_NAME = "Your Full Name Here";

// Your current position/job title
const MY_POSITION = "Your Current Position or Job Title";

// Your area of expertise or specialization
const POS_SEARCH = "Your Area of Expertise or Specialization";

// Advanced Configuration (Optional - you can customize these)
const CONFIG = {
    // Default connection limits
    DEFAULT_LIMIT: 100,        // Default limit for non-premium users
    PREMIUM_LIMIT: 200,        // Limit for premium users
    
    // Timing settings (in milliseconds)
    MIN_DELAY: 1000,          // Minimum delay between actions
    MAX_DELAY: 3000,          // Maximum delay between actions
    SCROLL_DELAY: 5000,       // Delay after scrolling
    
    // Message settings
    MESSAGE_SETTINGS: {
        // Set to true to include a personalized note with connection requests
        INCLUDE_NOTE: true,
        
        // Custom message template
        // Available variables: {firstName}, {MY_NAME}, {MY_POSITION}, {POS_SEARCH}
        CUSTOM_TEMPLATE: null  // Set to null to use default template
    },
    
    // UI Settings
    UI_SETTINGS: {
        PANEL_POSITION: 'top-right',  // 'top-right', 'top-left', 'bottom-right', 'bottom-left'
        SHOW_DETAILED_LOGS: true,     // Show detailed console logs
        AUTO_SCROLL: true             // Automatically scroll to load more results
    }
};

// Export for browser extension use
if (typeof window !== 'undefined') {
    window.LINKEDIN_CONFIG = {
        MY_NAME,
        MY_POSITION, 
        POS_SEARCH,
        ...CONFIG
    };
}

// Also make variables available globally for backward compatibility
if (typeof window !== 'undefined') {
    window.MY_NAME = MY_NAME;
    window.MY_POSITION = MY_POSITION;
    window.POS_SEARCH = POS_SEARCH;
}
