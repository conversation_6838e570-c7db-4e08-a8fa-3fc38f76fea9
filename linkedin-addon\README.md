# 🧩 LinkedIn Connect Browser Extension

Esta pasta contém os arquivos base para a extensão de navegador. **NÃO use esta pasta diretamente** - ela é apenas para sincronização com GitHub.

## 🚀 **Como Usar**

### 1. **Setup Inicial (Recomendado)**
```bash
# Na pasta raiz do projeto
copy config-master.template.js config.js
# Edite config.js com suas informações

# Execute sincronização inteligente
python sync-smart.py
```

### 2. **Build da Extensão**
```bash
# Na sua pasta pessoal (configurada no config.js)
python build-local.py
```

### 3. **Instalar no Navegador**

**Chrome/Edge/Opera:**
1. Abra `chrome://extensions/` (ou `edge://extensions/`)
2. Ative "Modo desenvolvedor"
3. "Carregar sem compactação" → selecione sua pasta pessoal/linkedin-addon/

**Firefox:**
1. Na sua pasta pessoal: `copy linkedin-addon\firefox\*.* linkedin-addon\`
2. Abra `about:debugging` → "Carregar extensão temporária"
3. Selecione `manifest.json` da sua pasta pessoal/linkedin-addon/

## 📁 **Estrutura desta Pasta (GitHub)**

```
linkedin-addon/
├── README.md                  ← Este arquivo
├── manifest.json              ← Manifest V3 (Chrome/Edge/Opera)
├── background.js              ← Background script V3
├── config.template.js         ← Template de configuração
├── icon.png                   ← Ícone da extensão
├── firefox/                   ← Arquivos específicos Firefox
│   ├── manifest.json          ← Manifest V2 (Firefox)
│   └── background.js          ← Background script V2
├── README-Chrome.md           ← Instruções Chrome
├── README-Edge.md             ← Instruções Edge
├── README-Firefox.md          ← Instruções Firefox
└── README-Opera.md            ← Instruções Opera
```

## ⚠️ **Arquivos NÃO Incluídos (Gerados Automaticamente)**

- `script.js` - Gerado a partir de "Adiciona Recrutadores Avançado.js"
- `config.js` - Suas configurações pessoais (não commitadas)

## 🔄 **Fluxo de Trabalho**

1. **Desenvolvimento**: Edite `Adiciona Recrutadores Avançado.js` na raiz
2. **Sincronização**: Execute `python sync-smart.py`
3. **Build**: Execute `python build-local.py` na pasta pessoal
4. **Uso**: Carregue a extensão da pasta pessoal

## 🧪 **Novas Funcionalidades**

### Modo de Teste
- ✅ Checkbox "Modo Teste" no painel de controle
- ✅ Pausa antes de cada envio para validação manual
- ✅ Preview da mensagem personalizada
- ✅ Confirmação individual de cada conexão

### Configuração Centralizada
- ✅ Um único arquivo `config.js` com todas as opções
- ✅ Template de mensagem personalizável
- ✅ Configuração de caminhos automática
- ✅ Sincronização inteligente

### Multi-Browser
- ✅ Chrome, Edge, Opera (Manifest V3)
- ✅ Firefox (Manifest V2)
- ✅ Arquivos específicos organizados

## 📖 **Documentação Completa**

- [GUIA-RAPIDO.md](../GUIA-RAPIDO.md) - Instruções rápidas
- [README.md](../README.md) - Documentação completa
- [config-master.template.js](../config-master.template.js) - Template de configuração

## 🔒 **Segurança**

- ✅ Dados pessoais nunca commitados
- ✅ Configurações separadas do código
- ✅ Sistema de backup automático
- ✅ Validação de segurança integrada
