# 🧩 LinkedIn Connect Browser Extension

Esta pasta contém os arquivos da extensão de navegador. É aqui que o `build-local.py` gera os arquivos finais para uso nos browsers.

## 🚀 **Como Usar**

### 1. **Setup Inicial**
```bash
# Na pasta raiz do projeto
copy config.template.js config.js
# Edite config.js com suas informações pessoais

# Gere a extensão
python build-local.py
```

### 2. **Instalar no Navegador**

**Chrome/Edge/Opera:**
1. Abra `chrome://extensions/` (ou `edge://extensions/`)
2. Ative "Modo desenvolvedor"
3. "Carregar sem compactação" → selecione esta pasta `linkedin-addon/`

**Firefox:**
1. Execute: `copy firefox\manifest.json manifest.json`
2. Execute: `copy firefox\background.js background.js`
3. Abra `about:debugging` → "Carregar extensão temporária"
4. Selecione `manifest.json` desta pasta

### 3. **Uso no Console do Browser (Alternativo)**
```javascript
// 1. Abra o console do LinkedIn (F12)
// 2. Cole e execute o conteúdo do arquivo config.js
// 3. Cole e execute o conteúdo do arquivo script.js
```

## 📁 **Estrutura desta Pasta (GitHub)**

```
linkedin-addon/
├── README.md                  ← Este arquivo
├── manifest.json              ← Manifest V3 (Chrome/Edge/Opera)
├── background.js              ← Background script V3
├── config.template.js         ← Template de configuração
├── icon.png                   ← Ícone da extensão
├── firefox/                   ← Arquivos específicos Firefox
│   ├── manifest.json          ← Manifest V2 (Firefox)
│   └── background.js          ← Background script V2
├── README-Chrome.md           ← Instruções Chrome
├── README-Edge.md             ← Instruções Edge
├── README-Firefox.md          ← Instruções Firefox
└── README-Opera.md            ← Instruções Opera
```

## ⚠️ **Arquivos NÃO Incluídos (Gerados Automaticamente)**

- `script.js` - Gerado a partir de "Adiciona Recrutadores Avançado.js"
- `config.js` - Suas configurações pessoais (não commitadas)

## 🔄 **Fluxo de Trabalho**

1. **Desenvolvimento**: Edite `Adiciona Recrutadores Avançado.js` na raiz
2. **Sincronização**: Execute `python sync-smart.py`
3. **Build**: Execute `python build-local.py` na pasta pessoal
4. **Uso**: Carregue a extensão da pasta pessoal

## 🧪 **Novas Funcionalidades**

### Modo de Teste
- ✅ Checkbox "Modo Teste" no painel de controle
- ✅ Pausa antes de cada envio para validação manual
- ✅ Preview da mensagem personalizada
- ✅ Confirmação individual de cada conexão

### Configuração Centralizada
- ✅ Um único arquivo `config.js` com todas as opções
- ✅ Template de mensagem personalizável
- ✅ Configuração de caminhos automática
- ✅ Sincronização inteligente

### Multi-Browser
- ✅ Chrome, Edge, Opera (Manifest V3)
- ✅ Firefox (Manifest V2)
- ✅ Arquivos específicos organizados

## 📖 **Documentação Completa**

- [GUIA-RAPIDO.md](../GUIA-RAPIDO.md) - Instruções rápidas
- [README.md](../README.md) - Documentação completa
- [config-master.template.js](../config-master.template.js) - Template de configuração

## 🔒 **Segurança**

- ✅ Dados pessoais nunca commitados
- ✅ Configurações separadas do código
- ✅ Sistema de backup automático
- ✅ Validação de segurança integrada
