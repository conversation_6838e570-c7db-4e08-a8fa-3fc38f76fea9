# 🚀 Quick Guide - LinkedIn Connect

## 📋 **Initial Setup**

### 1. **Simple Method (Recommended)**
```bash
# 1. Configure your information
copy config.template.js config.js
# Edit config.js with your personal information

# 2. Generate the extension
python build-local.py
```

This will:
- ✅ Create config.js with your information
- ✅ Generate script.js in the linkedin-addon/ folder
- ✅ Prepare extension for browser use
- ✅ **NEW**: Test mode with manual validation

### 2. **Browser Console Usage**
```bash
# 1. Configure your information
copy config.template.js config.js
# Edit config.js with your information

# 2. On LinkedIn, open console (F12)
# 3. Paste and execute the content of config.js
# 4. Paste and execute the content of "Adiciona Recrutadores Avançado.js"
```

## 🔧 **Using the Extension**

### Chrome/Edge/Opera (Simpler)
```bash
# In your personal or current folder
python build-local.py
```

Then:
1. Open `chrome://extensions/` (or `edge://extensions/`)
2. Enable "Developer mode"
3. "Load unpacked" → select `linkedin-addon/` folder

### Firefox
```bash
# In your personal or current folder
python build-local.py
cd linkedin-addon
copy firefox\manifest.json manifest.json
copy firefox\background.js background.js
```

Then:
1. Open `about:debugging`
2. "This Firefox" → "Load Temporary Add-on"
3. Select `manifest.json` from `linkedin-addon/` folder

## 🔄 **Future Updates**

### Smart System (Recommended)
```bash
# One command does everything
python sync-smart.py
```

### Traditional Method
```bash
# If using personal folder
python sync-personal.py
# Then in personal folder
python build-local.py

# If using current folder
python build-local.py
```

## 📁 **Simplified Structure**

```
Project/
├── Adiciona Recrutadores Avançado.js  ← Source file (you edit)
├── config.template.js                 ← Single template (copy to config.js)
├── config.js                          ← Your settings (you create)
├── build-local.py                     ← Generates extension
├── QUICK-GUIDE.md                     ← This guide
└── linkedin-addon/                    ← Extension folder (generated)
    ├── script.js                      ← Generated by build-local.py
    ├── config.js                      ← Copied by build-local.py
    ├── manifest.json, background.js   ← Extension files
    └── firefox/                       ← Firefox specific
        ├── manifest.json
        └── background.js
```

## 🔄 **Correct Workflow:**

1. **Configure**: `copy config.template.js config.js`
2. **Edit**: Open `config.js` and fill in your information
3. **Generate**: `python build-local.py` (creates script.js and copies config.js to linkedin-addon/)
4. **Use**: Load `linkedin-addon\` folder in browser

## ❓ **Frequently Asked Questions**

**Q: Why create a personal folder?**
A: To keep your personal data separate from the Git project.

**Q: Can I use it directly in the project folder?**
A: Yes! Just make sure not to commit the `config.js` files.

**Q: How do I update when there are changes in the project?**
A: Use `python sync-personal.py` if you have a personal folder, or `git pull` + `python build-local.py` if using current folder.

**Q: Is Firefox different?**
A: Just need to copy 2 files from the `firefox/` folder before using.

## 🎯 **Ultra-Quick Summary**

1. **First time**: `copy config.template.js config.js` → edit → `python build-local.py`
2. **Install**: Load `linkedin-addon/` folder in browser
3. **Update**: `python build-local.py` (after code changes)
4. **🧪 NEW**: Check "Test Mode" to manually validate messages

## 🧪 **Test Mode (NEW)**

- ✅ Enable "Test Mode" in the control panel
- ✅ **Limit = 1**: Stops script and **leaves window open** for you to decide manually
- ✅ **Limit > 1**: Shows preview in console, cancels invite and continues
- ✅ Message preview always in console
- ✅ **Full control**: With limit=1, you decide whether to send or cancel each invite
- ✅ Perfect for safely testing message templates

### **How to Use Test Mode:**

**Limit = 1 (Manual Validation):**
1. Check "Test Mode" and set limit = 1
2. Click "Start"
3. Script stops at first profile and **leaves window open**
4. See message preview in console
5. **You decide**: Click "Send" or "Cancel" in the window
6. Script remains paused for you to analyze

**Limit > 1 (Batch Preview):**
1. Check "Test Mode" and set limit > 1 (e.g., 5)
2. Click "Start"
3. Script shows preview of each message in console
4. Automatically cancels invites (doesn't send)
5. Continues until reaching the limit
6. Perfect for seeing how multiple messages look
