
# Instalação da Extensão no Microsoft Edge

## 📋 Pré-requisitos
- Microsoft Edge (versão baseada em Chromium)
- Arquivo `config.js` configurado com suas informações pessoais

## 🛠️ Preparação

### 1. Configure suas informações pessoais
```bash
# Na pasta linkedin-addon/
copy config.template.js config.js
```

Edite o arquivo `config.js` e preencha:
- `MY_NAME`: Seu nome completo
- `MY_POSITION`: Seu cargo atual
- `POS_SEARCH`: Sua área de especialização

## 🚀 Instalação

1. Abra o navegador e acesse: `edge://extensions`
2. Ative o **Modo de desenvolvedor** (canto inferior esquerdo)
3. Clique em **"Carregar sem compactação"**
4. Selecione a pasta `linkedin-addon/`
5. A extensão será carregada e aparecerá na lista

## 📱 Como Usar

1. Acesse o LinkedIn
2. Vá para a página de busca de pessoas
3. Clique no ícone da extensão na barra de ferramentas
4. Configure as opções no painel que aparece
5. Clique em "Start" para iniciar a automação

## 🔧 Solução de Problemas

**Erro: "Configurações não encontradas"**
- Verifique se você copiou `config.template.js` para `config.js`
- Certifique-se de que preencheu suas informações pessoais

**Extensão não carrega**
- Verifique se todos os arquivos estão na pasta
- Recarregue a extensão clicando no ícone de atualização

**Script não executa**
- Verifique se você está em uma página do LinkedIn
- Abra o console do desenvolvedor (F12) para ver erros
