# 🚀 Guia Rápido - LinkedIn Connect

## 📋 **Para Primeira Configuração**

### 1. **Configurar Pasta Pessoal (Recomendado)**
```bash
# Execute uma vez para criar sua pasta pessoal
python sync-personal.py
```

Isso vai:
- ✅ Criar uma pasta separada com seus dados pessoais
- ✅ Copiar todos os arquivos necessários
- ✅ Criar templates de configuração
- ✅ Manter seus dados seguros (fora do Git)

### 2. **Configurar na Pasta Atual**
```bash
# Se preferir trabalhar na pasta atual
copy config.template.js config.js
cd linkedin-addon
copy config.template.js config.js
# Edite ambos os config.js com suas informações
```

## 🔧 **Para Usar a Extensão**

### Chrome/Edge/Opera (Mais Simples)
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
```

Depois:
1. Abra `chrome://extensions/` (ou `edge://extensions/`)
2. Ative "Modo desenvolvedor"
3. "Carregar sem compactação" → selecione pasta `linkedin-addon/`

### Firefox
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
cd linkedin-addon
copy firefox\manifest.json manifest.json
copy firefox\background.js background.js
```

Depois:
1. Abra `about:debugging`
2. "Este Firefox" → "Carregar extensão temporária"
3. Selecione `manifest.json` da pasta `linkedin-addon/`

## 🔄 **Para Atualizações Futuras**

### Se Usa Pasta Pessoal
```bash
# Execute no projeto original
python sync-personal.py
# Depois na pasta pessoal
python build-local.py
```

### Se Usa Pasta Atual
```bash
# Apenas execute
python build-local.py
```

## 📁 **Estrutura Simplificada**

```
Projeto Original/
├── Adiciona Recrutadores Avançado.js  ← Arquivo fonte principal
├── sync-personal.py                   ← Para sincronizar pasta pessoal
├── build-local.py                     ← Para gerar extensão
└── linkedin-addon/
    ├── firefox/                       ← Apenas arquivos específicos Firefox
    │   ├── manifest.json
    │   └── background.js
    ├── manifest.json                  ← Para Chrome/Edge/Opera
    ├── background.js                  ← Para Chrome/Edge/Opera
    ├── script.js                      ← Gerado automaticamente
    ├── config.template.js             ← Template seguro
    └── config.js                      ← Suas configurações (não commitado)

Pasta Pessoal/ (opcional)
├── config.js                          ← Suas configurações principais
├── build-local.py                     ← Copiado automaticamente
└── linkedin-addon/
    ├── config.js                      ← Suas configurações da extensão
    └── (outros arquivos...)
```

## ❓ **Dúvidas Frequentes**

**P: Por que criar pasta pessoal?**
R: Para manter seus dados pessoais separados do projeto Git.

**P: Posso usar direto na pasta do projeto?**
R: Sim! Apenas certifique-se de não commitar os arquivos `config.js`.

**P: Como atualizo quando há mudanças no projeto?**
R: Use `python sync-personal.py` se tem pasta pessoal, ou `git pull` + `python build-local.py` se usa pasta atual.

**P: Firefox é diferente?**
R: Apenas precisa copiar 2 arquivos da pasta `firefox/` antes de usar.

## 🎯 **Resumo Ultra-Rápido**

1. **Primeira vez**: `python sync-personal.py` (ou configure config.js manualmente)
2. **Gerar extensão**: `python build-local.py`
3. **Instalar**: Carregar pasta `linkedin-addon/` no navegador
4. **Atualizar**: `python sync-personal.py` + `python build-local.py`
