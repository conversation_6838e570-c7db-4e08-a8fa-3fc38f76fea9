# 🚀 Guia Rápido - LinkedIn Connect

## 📋 **Para Primeira Configuração**

### 1. **Método Simples (Recomendado)**
```bash
# 1. Configure suas informações
copy linkedin-addon\config.template.js linkedin-addon\config.js
# Edite linkedin-addon\config.js com suas informações pessoais

# 2. Gere a extensão
python build-local.py
```

Isso vai:
- ✅ Criar config.js com suas informações
- ✅ Gerar script.js na pasta linkedin-addon/
- ✅ Preparar extensão para uso nos browsers
- ✅ **NOVO**: Modo de teste com validação manual

### 2. **Uso no Console do Browser**
```bash
# 1. Configure suas informações
copy linkedin-addon\config.template.js config.js
# Edite config.js com suas informações

# 2. No LinkedIn, abra console (F12)
# 3. <PERSON> e execute o conteúdo do config.js
# 4. <PERSON> e execute o conteúdo do "Adiciona Recrutadores Avançado.js"
```

## 🔧 **Para Usar a Extensão**

### Chrome/Edge/Opera (Mais Simples)
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
```

Depois:
1. Abra `chrome://extensions/` (ou `edge://extensions/`)
2. Ative "Modo desenvolvedor"
3. "Carregar sem compactação" → selecione pasta `linkedin-addon/`

### Firefox
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
cd linkedin-addon
copy firefox\manifest.json manifest.json
copy firefox\background.js background.js
```

Depois:
1. Abra `about:debugging`
2. "Este Firefox" → "Carregar extensão temporária"
3. Selecione `manifest.json` da pasta `linkedin-addon/`

## 🔄 **Para Atualizações Futuras**

### Sistema Inteligente (Recomendado)
```bash
# Um comando faz tudo
python sync-smart.py
```

### Método Tradicional
```bash
# Se usa pasta pessoal
python sync-personal.py
# Depois na pasta pessoal
python build-local.py

# Se usa pasta atual
python build-local.py
```

## 📁 **Estrutura Simplificada**

```
Projeto/
├── Adiciona Recrutadores Avançado.js  ← Arquivo fonte (você edita)
├── build-local.py                     ← Gera extensão
├── GUIA-RAPIDO.md                     ← Este guia
└── linkedin-addon/                    ← Pasta da extensão
    ├── config.template.js             ← Template (copie para config.js)
    ├── config.example.js              ← Exemplo preenchido
    ├── config.js                      ← Suas configurações (você cria)
    ├── script.js                      ← Gerado pelo build-local.py
    ├── manifest.json, background.js   ← Arquivos da extensão
    └── firefox/                       ← Específicos Firefox
        ├── manifest.json
        └── background.js
```

## 🔄 **Fluxo Correto:**

1. **Configure**: `copy linkedin-addon\config.template.js linkedin-addon\config.js`
2. **Edite**: Abra `linkedin-addon\config.js` e preencha suas informações
3. **Gere**: `python build-local.py` (cria script.js)
4. **Use**: Carregue pasta `linkedin-addon\` no navegador

## ❓ **Dúvidas Frequentes**

**P: Por que criar pasta pessoal?**
R: Para manter seus dados pessoais separados do projeto Git.

**P: Posso usar direto na pasta do projeto?**
R: Sim! Apenas certifique-se de não commitar os arquivos `config.js`.

**P: Como atualizo quando há mudanças no projeto?**
R: Use `python sync-personal.py` se tem pasta pessoal, ou `git pull` + `python build-local.py` se usa pasta atual.

**P: Firefox é diferente?**
R: Apenas precisa copiar 2 arquivos da pasta `firefox/` antes de usar.

## 🎯 **Resumo Ultra-Rápido**

1. **Primeira vez**: `copy linkedin-addon\config.template.js linkedin-addon\config.js` → editar → `python build-local.py`
2. **Instalar**: Carregar pasta `linkedin-addon/` no navegador
3. **Atualizar**: `python build-local.py` (após mudanças no código)
4. **🧪 NOVO**: Marque "Modo Teste" para validar mensagens manualmente

## 🧪 **Modo de Teste (NOVO)**

- ✅ Ative "Modo Teste" no painel de controle
- ✅ **Limite = 1**: Para após cada envio para validação manual completa
- ✅ **Limite > 1**: Mostra preview no console, cancela convite e continua
- ✅ Preview da mensagem sempre no console
- ✅ Perfeito para testar templates de mensagem sem enviar
