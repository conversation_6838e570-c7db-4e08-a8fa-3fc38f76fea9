# 🚀 Guia Rápido - LinkedIn Connect

## 📋 **Para Primeira Configuração**

### 1. **Método Simples (Recomendado)**
```bash
# 1. Configure suas informações
copy config.template.js config.js
# Edite config.js com suas informações pessoais

# 2. Gere a extensão
python build-local.py
```

Isso vai:
- ✅ Criar config.js com suas informações
- ✅ Gerar script.js na pasta linkedin-addon/
- ✅ Preparar extensão para uso nos browsers
- ✅ **NOVO**: Modo de teste com validação manual

### 2. **Uso no Console do Browser**
```bash
# 1. Configure suas informações
copy config.template.js config.js
# Edite config.js com suas informações

# 2. No LinkedIn, abra console (F12)
# 3. Cole e execute o conteúdo do config.js
# 4. <PERSON> e execute o conteúdo do "Adiciona Recrutadores Avançado.js"
```

## 🔧 **Para Usar a Extensão**

### Chrome/Edge/Opera (Mais Simples)
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
```

Depois:
1. Abra `chrome://extensions/` (ou `edge://extensions/`)
2. Ative "Modo desenvolvedor"
3. "Carregar sem compactação" → selecione pasta `linkedin-addon/`

### Firefox
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
cd linkedin-addon
copy firefox\manifest.json manifest.json
copy firefox\background.js background.js
```

Depois:
1. Abra `about:debugging`
2. "Este Firefox" → "Carregar extensão temporária"
3. Selecione `manifest.json` da pasta `linkedin-addon/`

## 🔄 **Para Atualizações Futuras**

### Sistema Inteligente (Recomendado)
```bash
# Um comando faz tudo
python sync-smart.py
```

### Método Tradicional
```bash
# Se usa pasta pessoal
python sync-personal.py
# Depois na pasta pessoal
python build-local.py

# Se usa pasta atual
python build-local.py
```

## 📁 **Estrutura Simplificada**

```
Projeto/
├── Adiciona Recrutadores Avançado.js  ← Arquivo fonte (você edita)
├── config.template.js                 ← Template único (copie para config.js)
├── config.js                          ← Suas configurações (você cria)
├── build-local.py                     ← Gera extensão
├── GUIA-RAPIDO.md                     ← Este guia
└── linkedin-addon/                    ← Pasta da extensão (gerada)
    ├── script.js                      ← Gerado pelo build-local.py
    ├── config.js                      ← Copiado pelo build-local.py
    ├── manifest.json, background.js   ← Arquivos da extensão
    └── firefox/                       ← Específicos Firefox
        ├── manifest.json
        └── background.js
```

## 🔄 **Fluxo Correto:**

1. **Configure**: `copy config.template.js config.js`
2. **Edite**: Abra `config.js` e preencha suas informações
3. **Gere**: `python build-local.py` (cria script.js e copia config.js para linkedin-addon/)
4. **Use**: Carregue pasta `linkedin-addon\` no navegador

## ❓ **Dúvidas Frequentes**

**P: Por que criar pasta pessoal?**
R: Para manter seus dados pessoais separados do projeto Git.

**P: Posso usar direto na pasta do projeto?**
R: Sim! Apenas certifique-se de não commitar os arquivos `config.js`.

**P: Como atualizo quando há mudanças no projeto?**
R: Use `python sync-personal.py` se tem pasta pessoal, ou `git pull` + `python build-local.py` se usa pasta atual.

**P: Firefox é diferente?**
R: Apenas precisa copiar 2 arquivos da pasta `firefox/` antes de usar.

## 🎯 **Resumo Ultra-Rápido**

1. **Primeira vez**: `copy config.template.js config.js` → editar → `python build-local.py`
2. **Instalar**: Carregar pasta `linkedin-addon/` no navegador
3. **Atualizar**: `python build-local.py` (após mudanças no código)
4. **🧪 NOVO**: Marque "Modo Teste" para validar mensagens manualmente

## 🧪 **Modo de Teste (NOVO)**

- ✅ Ative "Modo Teste" no painel de controle
- ✅ **Limite = 1**: Para o script e **deixa janela aberta** para você decidir manualmente
- ✅ **Limite > 1**: Mostra preview no console, cancela convite e continua
- ✅ Preview da mensagem sempre no console
- ✅ **Controle total**: No limite=1, você decide se envia ou cancela cada convite
- ✅ Perfeito para testar templates de mensagem com segurança

### **Como Usar o Modo Teste:**

**Limite = 1 (Validação Manual):**
1. Marque "Modo Teste" e defina limite = 1
2. Clique "Start"
3. Script para no primeiro perfil e **deixa janela aberta**
4. Veja preview da mensagem no console
5. **Você decide**: Clique "Send" ou "Cancel" na janela
6. Script permanece pausado para você analisar

**Limite > 1 (Preview em Lote):**
1. Marque "Modo Teste" e defina limite > 1 (ex: 5)
2. Clique "Start"
3. Script mostra preview de cada mensagem no console
4. Cancela automaticamente os convites (não envia)
5. Continua até atingir o limite
6. Perfeito para ver como ficam várias mensagens
