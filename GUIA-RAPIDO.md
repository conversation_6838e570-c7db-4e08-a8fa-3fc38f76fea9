# 🚀 G<PERSON><PERSON>pido - LinkedIn Connect

## 📋 **Para Primeira Configuração**

### 1. **Sistema Inteligente (Novo e Recomendado)**
```bash
# 1. Configure uma vez
copy config-master.template.js config.js
# Edite config.js com suas informações e caminhos

# 2. Execute sincronização inteligente
python sync-smart.py
```

Isso vai:
- ✅ Usar suas configurações para automatizar tudo
- ✅ Criar pastas automaticamente conforme configurado
- ✅ Sincronizar preservando dados pessoais
- ✅ Preparar extensão para GitHub
- ✅ **NOVO**: Modo de teste com validação manual

### 2. **Método Tradicional (Pasta Atual)**
```bash
# Se preferir trabalhar na pasta atual
copy config-master.template.js config.js
# Edite config.js com suas informações
python sync-smart.py
```

### 3. **Método Manual (Compatibilidade)**
```bash
copy config.template.js config.js
cd linkedin-addon
copy config.template.js config.js
# Edite ambos os config.js com suas informações
```

## 🔧 **Para Usar a Extensão**

### Chrome/Edge/Opera (Mais Simples)
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
```

Depois:
1. Abra `chrome://extensions/` (ou `edge://extensions/`)
2. Ative "Modo desenvolvedor"
3. "Carregar sem compactação" → selecione pasta `linkedin-addon/`

### Firefox
```bash
# Na sua pasta pessoal ou pasta atual
python build-local.py
cd linkedin-addon
copy firefox\manifest.json manifest.json
copy firefox\background.js background.js
```

Depois:
1. Abra `about:debugging`
2. "Este Firefox" → "Carregar extensão temporária"
3. Selecione `manifest.json` da pasta `linkedin-addon/`

## 🔄 **Para Atualizações Futuras**

### Sistema Inteligente (Recomendado)
```bash
# Um comando faz tudo
python sync-smart.py
```

### Método Tradicional
```bash
# Se usa pasta pessoal
python sync-personal.py
# Depois na pasta pessoal
python build-local.py

# Se usa pasta atual
python build-local.py
```

## 📁 **Estrutura Simplificada**

```
Projeto Original/
├── Adiciona Recrutadores Avançado.js  ← Arquivo fonte principal
├── sync-personal.py                   ← Para sincronizar pasta pessoal
├── build-local.py                     ← Para gerar extensão
└── linkedin-addon/
    ├── firefox/                       ← Apenas arquivos específicos Firefox
    │   ├── manifest.json
    │   └── background.js
    ├── manifest.json                  ← Para Chrome/Edge/Opera
    ├── background.js                  ← Para Chrome/Edge/Opera
    ├── script.js                      ← Gerado automaticamente
    ├── config.template.js             ← Template seguro
    └── config.js                      ← Suas configurações (não commitado)

Pasta Pessoal/ (opcional)
├── config.js                          ← Suas configurações principais
├── build-local.py                     ← Copiado automaticamente
└── linkedin-addon/
    ├── config.js                      ← Suas configurações da extensão
    └── (outros arquivos...)
```

## ❓ **Dúvidas Frequentes**

**P: Por que criar pasta pessoal?**
R: Para manter seus dados pessoais separados do projeto Git.

**P: Posso usar direto na pasta do projeto?**
R: Sim! Apenas certifique-se de não commitar os arquivos `config.js`.

**P: Como atualizo quando há mudanças no projeto?**
R: Use `python sync-personal.py` se tem pasta pessoal, ou `git pull` + `python build-local.py` se usa pasta atual.

**P: Firefox é diferente?**
R: Apenas precisa copiar 2 arquivos da pasta `firefox/` antes de usar.

## 🎯 **Resumo Ultra-Rápido**

1. **Primeira vez**: `copy config-master.template.js config.js` → editar → `python sync-smart.py`
2. **Gerar extensão**: `python build-local.py` (na pasta pessoal)
3. **Instalar**: Carregar pasta `linkedin-addon/` no navegador
4. **Atualizar**: `python sync-smart.py`
5. **🧪 NOVO**: Marque "Modo Teste" para validar mensagens manualmente

## 🧪 **Modo de Teste (NOVO)**

- ✅ Ative "Modo Teste" no painel de controle
- ✅ Script pausa antes de cada envio
- ✅ Mostra preview da mensagem
- ✅ Você confirma ou cancela cada conexão
- ✅ Perfeito para testar templates de mensagem
