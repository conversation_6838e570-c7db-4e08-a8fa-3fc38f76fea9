# Instalação da Extensão no Mozilla Firefox

## 📋 Pré-requisitos
- Firefox 57 ou superior
- Arquivo `config.js` configurado com suas informações pessoais

## 🛠️ Preparação

### 1. Configure suas informações pessoais
```bash
# Na pasta linkedin-addon/
copy config.template.js config.js
```

Edite o arquivo `config.js` e preencha:
- `MY_NAME`: Seu nome completo
- `MY_POSITION`: Seu cargo atual
- `POS_SEARCH`: Sua área de especialização

### 2. Prepare os arquivos para Firefox
```bash
# Copie o manifest específico do Firefox
copy manifest-firefox.json manifest.json
```

## 🚀 Instalação

### Método 1: Instalação Temporária (Desenvolvimento)

1. Abra o Firefox e digite na barra de endereços: `about:debugging`
2. Clique em **"Este Firefox"** (This Firefox)
3. Clique em **"Carregar extensão temporária..."** (Load Temporary Add-on...)
4. Navegue até a pasta `linkedin-addon/` e selecione o arquivo `manifest.json`
5. A extensão será carregada temporariamente

⚠️ **Nota**: Extensões temporárias são removidas quando o Firefox é fechado.

### Método 2: Instalação Permanente (Assinada)

Para instalação permanente, a extensão precisa ser assinada pela Mozilla:

1. Crie uma conta em [addons.mozilla.org](https://addons.mozilla.org/developers/)
2. Faça upload da extensão para revisão
3. Após aprovação, instale normalmente

### Método 3: Firefox Developer Edition (Sem assinatura)

1. Baixe o [Firefox Developer Edition](https://www.mozilla.org/firefox/developer/)
2. Digite `about:config` na barra de endereços
3. Procure por `xpinstall.signatures.required`
4. Altere o valor para `false`
5. Agora você pode instalar extensões não assinadas permanentemente

## 📱 Como Usar

1. Acesse o LinkedIn
2. Vá para a página de busca de pessoas
3. Clique no ícone da extensão na barra de ferramentas
4. Configure as opções no painel que aparece
5. Clique em "Start" para iniciar a automação

## 🔧 Solução de Problemas

**Erro: "config.js não encontrado"**
- Verifique se você copiou `config.template.js` para `config.js`
- Certifique-se de que o arquivo está na pasta `linkedin-addon/`

**Extensão não aparece**
- Verifique se o arquivo `manifest.json` é o correto para Firefox
- Recarregue a extensão em `about:debugging`

**Script não executa**
- Verifique se você está em uma página do LinkedIn
- Abra o console do desenvolvedor (F12) para ver erros

## 📁 Estrutura de Arquivos para Firefox

```
linkedin-addon/
├── manifest.json (copiado de manifest-firefox.json)
├── background-firefox.js
├── script.js
├── config.js (suas informações pessoais)
├── config.template.js
└── icon.png
```

## 🔄 Atualizações

Para atualizar a extensão:
1. Substitua os arquivos na pasta
2. Em `about:debugging`, clique em "Recarregar" na extensão
3. Ou remova e reinstale a extensão

## ⚠️ Limitações do Firefox

- Extensões temporárias são removidas ao fechar o navegador
- Para instalação permanente, é necessária assinatura da Mozilla
- Use Firefox Developer Edition para desenvolvimento sem assinatura
